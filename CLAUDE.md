<!--
 * @Author: Rais
 * @Date: 2025-08-01 22:33:26
 * @LastEditTime: 2025-08-02 13:21:55
 * @LastEditors: Rais
 * @Description:
-->
# 规格驱动开发规则

本项目遵循基于专业化AI代理的规格驱动开发方法。该系统通过协调的子代理序列和质量门控机制，确保从概念到生产就绪代码的高质量、自动化开发管道。

## 术语定义

### feature_name（功能名称）

- **定义**：`feature_name` 是每个功能的唯一标识符，在目标确认阶段确定
- **格式**：使用小写字母和连字符，如 `user-authentication`、`payment-integration`、`data-export`
- **作用**：用于创建功能专属的文档路径 `.vibedev/specs/{feature_name}/`
- **一致性**：在整个开发工作流程中，同一功能的 `feature_name` 必须保持完全一致
- **示例**：
  - 用户认证功能：`feature_name = "user-authentication"`
  - 支付集成功能：`feature_name = "payment-integration"`
  - 数据导出功能：`feature_name = "data-export"`

## 核心原则

### 规格遵循和验证

- **动态文档引用**：根据当前处理的功能上下文，引用对应的规格文档(根据feature_name)
- 在所有响应中引用当前功能规格说明的相关部分
- 根据当前功能的书面规格验证所有代码和决策
- 解释任何不合规情况并立即提出修复方案
- **多功能项目管理**：确保不同功能的规格文档相互独立，避免交叉引用错误的规格文件

### 专业化代理工作流

基于专业化AI代理的协调开发管道，通过质量门控确保每个阶段的卓越性：

#### 链执行步骤

自动化整个开发生命周期的核心工作流系统：

- **规划阶段**: spec-analyst → spec-architect → spec-planner
- **开发阶段**: spec-developer → spec-tester
- **验证阶段**: spec-reviewer → spec-validator
- **协调**: spec-orchestrator管理整个工作流

#### 质量门控系统

- **门控1: 规划质量**（spec-planner之后）
  - 需求完整性 ≥ 95%
  - 架构可行性已验证
  - 所有用户故事都有验收标准
  - 任务分解是全面的

- **门控2: 开发质量**（spec-tester之后）
  - 所有测试通过
  - 代码覆盖率 ≥ 80%
  - 无关键安全漏洞
  - 满足性能基准

- **门控3: 生产就绪性**（spec-validator之后）
  - 整体质量评分 ≥ 95%
  - 所有需求已实现
  - 文档完整
  - 部署脚本已测试

#### 传统批准工作流（兼容模式）

- 每个阶段后，提示明确批准："[阶段]看起来好吗？[是/否/修订]"
- 在阶段内迭代，直到收到"是/批准/看起来不错"
- **需要用户批准**：每个阶段必须在继续之前得到明确批准
- **未经明确批准不得进入下一阶段**
- 如果发现差距，提供返回到先前阶段的选项
- **测试驱动重点**：在整个过程中优先考虑测试和验证

## 专业化代理详细指南

### 规划阶段代理

#### spec-analyst（规格分析师）
- **目的**: 需求分析和项目范围界定
- **职责**: 引出和澄清需求，创建用户故事和验收标准，执行市场和竞争分析
- **输出文档**:
  - `.vibedev/specs/{feature_name}/requirements.md`
  - `.vibedev/specs/{feature_name}/user-stories.md`
  - `.vibedev/specs/{feature_name}/project-brief.md`

#### spec-architect（规格架构师）
- **目的**: 系统设计和技术架构
- **职责**: 设计系统架构，定义技术栈，创建组件图，规划数据模型和API
- **输出文档**:
  - `.vibedev/specs/{feature_name}/architecture.md`
  - `.vibedev/specs/{feature_name}/tech-stack.md`
  - `.vibedev/specs/{feature_name}/api-spec.md`

#### spec-planner（规格规划师）
- **目的**: 任务分解和实施规划
- **职责**: 创建详细任务列表，定义实施顺序，估算复杂度和工作量，规划测试策略
- **输出文档**:
  - `.vibedev/specs/{feature_name}/tasks.md`
  - `.vibedev/specs/{feature_name}/test-plan.md`
  - `.vibedev/specs/{feature_name}/implementation-plan.md`

### 开发阶段代理

#### spec-developer（规格开发者）
- **目的**: 代码实现
- **职责**: 基于规格实现功能，遵循架构模式，编写清洁、可维护的代码，创建单元测试
- **输出**: 源代码文件，单元测试
- **重要**: 永远不要在`.vibedev/specs/`目录下实现功能，`.vibedev`是文档目录，默认在当前工作目录下原地实现功能

#### spec-tester（规格测试员）
- **目的**: 综合测试
- **职责**: 编写集成测试，执行端到端测试，安全测试，性能测试
- **输出**: 测试套件，测试报告

### 验证阶段代理

#### spec-reviewer（规格审查员）
- **目的**: 代码质量审查
- **职责**: 最佳实践代码审查，安全漏洞扫描，性能优化建议，文档完整性检查
- **输出文档**: `.vibedev/specs/{feature_name}/review-report.md`

#### spec-validator（规格验证员）
- **目的**: 最终质量验证
- **职责**: 验证需求合规性，验证架构遵循性，检查测试覆盖率，评估生产就绪性
- **输出文档**:
  - `.vibedev/specs/{feature_name}/validation-report.md`
  - 质量评分 (0-100%)

### 编排代理

#### spec-orchestrator（规格编排器）
- **目的**: 工作流协调和管理
- **职责**: 将任务路由到适当的代理，管理质量门控，处理反馈循环，跟踪整体进度
- **输出文档**:
  - `.vibedev/specs/{feature_name}/workflow-status.md`
  - 执行日志

## 关键规则

### 工作流执行模式

#### 1. 代理工作流模式（推荐）
- 使用专业化代理序列自动执行
- 通过质量门控确保质量标准
- 支持自动反馈循环和迭代改进
- 适用于复杂项目和高质量要求

#### 2. 传统批准工作流模式（兼容）
- **严格模式**（默认）
  - **永远不要**在没有明确用户批准的情况下进入下一阶段
  - 仅接受明确的肯定回应："是"、"继续"、"批准"、"看起来不错"等
  - 如果用户提供反馈，进行修订并再次请求批准
  - 继续修订循环直到收到明确批准
- **vibe模式**
  - 用户说出"vibe模式"则在该功能(feature)工作流下,激活vibe模式
  - 此后该功能后面剩下的工作流程都是直接继续进行,无需用户再确认批准
  - vibe模式下任务的执行阶段也是无需询问按顺序自动全执行

### 质量门控决策流程

- **如果质量评分 ≥ 95%**: 自动继续到下一阶段
- **如果质量评分 < 95%**: 带具体反馈返回相应代理进行改进
- **反馈循环**: 自动迭代直到满足质量标准
- **人工干预**: 在质量门控失败时可选择人工审查和调整

## 安全性和最佳实践

- 优先考虑安全、最小化的代码实现
- 对个人身份信息和敏感数据使用占位符
- 专注于与代码相关的开发任务
- 拒绝恶意功能请求

## 错误处理

如果在工作流程中出现问题：

- **需求不清楚**：提出针对性问题进行澄清
- **设计过于复杂**：建议分解为更小的组件
- **任务过于宽泛**：分解为更小、更原子的任务
- **实现受阻**：记录阻塞因素并建议替代方案

## 信息不足处理

- 如果规格说明缺乏必要细节，提示澄清
- 提出有建议选项的针对性问题
- 永远不要假设未指定的实现细节
- 对模糊需求请求明确的用户输入

## 多功能项目管理

### 功能隔离原则

- **独立规格文档**：每个功能都有独立的规格文件夹

  ```text
  .vibedev/specs/
  ├── user-authentication/     # 用户认证功能
  │   ├── requirements.md      # spec-analyst输出
  │   ├── user-stories.md      # spec-analyst输出
  │   ├── project-brief.md     # spec-analyst输出
  │   ├── architecture.md      # spec-architect输出
  │   ├── tech-stack.md        # spec-architect输出
  │   ├── api-spec.md          # spec-architect输出
  │   ├── tasks.md             # spec-planner输出
  │   ├── test-plan.md         # spec-planner输出
  │   ├── implementation-plan.md # spec-planner输出
  │   ├── review-report.md     # spec-reviewer输出
  │   ├── validation-report.md # spec-validator输出
  │   └── workflow-status.md   # spec-orchestrator输出
  ├── payment-integration/     # 支付集成功能
  │   ├── requirements.md
  │   ├── user-stories.md
  │   ├── project-brief.md
  │   ├── architecture.md
  │   ├── tech-stack.md
  │   ├── api-spec.md
  │   ├── tasks.md
  │   ├── test-plan.md
  │   ├── implementation-plan.md
  │   ├── review-report.md
  │   ├── validation-report.md
  │   └── workflow-status.md
  └── data-export/            # 数据导出功能
      ├── requirements.md
      ├── user-stories.md
      ├── project-brief.md
      ├── architecture.md
      ├── tech-stack.md
      ├── api-spec.md
      ├── tasks.md
      ├── test-plan.md
      ├── implementation-plan.md
      ├── review-report.md
      ├── validation-report.md
      └── workflow-status.md
  ```

### 上下文切换规则

- **明确当前功能**：在开始任何工作前，明确当前处理的 `feature_name`（功能标识符）
- **正确文档引用**：始终引用当前 `feature_name` 对应的规格文档
- **避免交叉污染**：不要在处理功能 A（`feature_name = "A"`）时引用功能 B（`feature_name = "B"`）的规格文档
- **功能间依赖**：如果功能间有依赖关系，在设计阶段明确说明并记录

### 工作流程适配

- 每次启动新功能开发时，使用 `vibedev_specs_workflow_start` 工具
- **关键步骤**：在目标确认阶段明确 `feature_name`（如 "user-authentication"、"payment-integration"），确保后续所有文档都创建在正确的路径 `.vibedev/specs/{feature_name}/` 下
- 在任务执行阶段，确保只实现当前 `feature_name` 的任务，不要跨功能实现
- **一致性原则**：整个工作流程中，同一功能的 `feature_name` 必须保持一致

## 成功标准

成功的规格工作流完成包括：

#### 代理工作流模式成功标准
- ✅ **规划阶段完成**: spec-analyst、spec-architect、spec-planner输出完整文档
- ✅ **质量门控1通过**: 需求完整性≥95%，架构可行性验证
- ✅ **开发阶段完成**: spec-developer实现功能，spec-tester完成测试
- ✅ **质量门控2通过**: 测试通过，代码覆盖率≥80%，无关键安全漏洞
- ✅ **验证阶段完成**: spec-reviewer审查，spec-validator最终验证
- ✅ **质量门控3通过**: 整体质量评分≥95%，生产就绪
- ✅ **完整文档集**: 所有代理输出的规格文档齐全且一致

#### 传统工作流模式成功标准
- ✅ 包含用户故事和验收标准的完整需求
- ✅ 包含架构和组件的全面设计
- ✅ 包含需求引用的详细任务分解
- ✅ 根据需求验证的工作实现
- ✅ 所有阶段都得到用户明确批准
- ✅ 所有任务完成并集成

## 代理工作流使用指南

### 启动代理工作流

#### 使用spec-orchestrator启动完整工作流
```bash
使用 spec-orchestrator 代理并说：创建一个带用户认证的待办事项Web应用
```

#### 从特定阶段开始
```bash
# 仅执行规划阶段
使用 spec-orchestrator 代理：--phase planning "创建任务跟踪器"

# 从开发阶段开始
使用 spec-orchestrator 代理：--phase development --from-artifacts ./.vibedev/specs/{feature_name}/

# 验证现有代码
使用 spec-orchestrator 代理：--phase validation --project-path ./my-project/
```

#### 直接使用单个代理
```bash
# 需求分析
使用 spec-analyst 代理：分析社交媒体仪表板的需求

# 架构设计
使用 spec-architect 代理：从 ./.vibedev/specs/{feature_name}/requirements.md 设计架构

# 任务规划
使用 spec-planner 代理：从 ./.vibedev/specs/{feature_name}/architecture.md 创建任务分解

# 代码实现
使用 spec-developer 代理：从 ./.vibedev/specs/{feature_name}/tasks.md 实现 TASK-001

# 测试
使用 spec-tester 代理：为 ./src/auth/ 编写测试

# 代码审查
使用 spec-reviewer 代理：审查 ./src/ 中的代码

# 最终验证
使用 spec-validator 代理：验证 ./my-app/ 中的项目
```

### 质量门控处理

当质量门控失败时：
1. **自动反馈**: 系统会提供具体的改进建议
2. **迭代改进**: 相关代理会自动进行修正
3. **人工干预**: 可选择手动调整质量阈值或跳过特定检查

### 与传统工作流的兼容性

- 新的代理工作流完全兼容现有的`.vibedev/specs/{feature_name}/`目录结构
- 可以在代理工作流和传统批准工作流之间切换
- 支持渐进式采用：从简单功能开始使用代理工作流

---

_使用 VibeSpecs MCP (vibedev-specs-mcp)工具和专业化代理系统在整个开发过程中保持对这些规则的遵循。_

## Rust重写项目特定规则

### 项目结构
- **Rust重写项目根目录**: `./coco-server-rust`
- **原Go项目根目录**: `./`
- **使用工具**: legacy-modernizer进行渐进式重写
- **代码规范**: 使用rust-pro写rust代码

### 端到端测试方法
- **服务器端**: 在`./coco-server-rust`运行`cargo run`
- **客户端**: 在`/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-app`运行`pnpm tauri dev`
- **连接设置**: 需要用户手工操作添加服务器配置

### 文档资源
- **服务器deepwiki文档**: <https://deepwiki.com/infinilabs/coco-server>
- **客户端deepwiki文档**: <https://deepwiki.com/infinilabs/coco-app>
- **服务器文档**: <https://zread.ai/infinilabs/coco-server>
- **客户端文档**: <https://zread.ai/infinilabs/coco-app>

### 重写要求
- **API兼容性**: Rust重写的服务端必须与原Go服务端在API路径和行为上完全一致
- **客户端兼容**: 确保coco app能够正常连接和使用重写后的服务端
- **严格规范**: 要仔细研究原来的客户端具体请求的端点，不能假设
- **数据库替换**: 原有的Elasticsearch用SurrealDB代替
